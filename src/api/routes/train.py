"""
API routes for model training.
"""

import re

from fastapi import APIRouter, HTTPException, Request, status
from pydantic import BaseModel, Field

from api.utils import get_supabase_profile
from api.utils.error_handlers import INTERNAL_SERVER_ERROR_RESPONSE, api_route_handler
from database.models import Dataset, ModelVersion
from database.services import TrainingDataError, TrainingDataService

router = APIRouter(
    prefix="/train",
    tags=["training"],  # Groups endpoints under "training" in the docs
    responses=INTERNAL_SERVER_ERROR_RESPONSE,
)


class TrainRequest(BaseModel):
    """Request model for initiating model training."""

    model_run_uuid: str = Field(
        ..., description="UUID of the model run containing training configuration"
    )

    class Config:
        """Example for the documentation."""

        json_schema_extra = {
            "example": {
                "model_run_uuid": "uuid_run_123",
            }
        }

        @classmethod
        def model_json_schema_extra(cls, schema: dict) -> None:
            """Add extra information to the schema."""
            schema.update(
                {
                    "title": "Train Request",
                    "description": "Request model for initiating model training",
                }
            )

        @classmethod
        def get_example(cls) -> dict:
            """Return the example data."""
            return cls.json_schema_extra["example"]

    def validate_uuid_format(self) -> bool:
        """Validate that the model_run_uuid is in the correct format."""
        uuid_pattern = re.compile(
            r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$",
            re.IGNORECASE,
        )
        return bool(uuid_pattern.match(self.model_run_uuid))

    def get_model_run_id(self) -> str:
        """Return the model run UUID in a standardized format."""
        return str(self.model_run_uuid).lower()


class TrainResponse(BaseModel):
    """Response model for the train endpoint."""

    status: str = Field(..., description="Status of the training request")
    model_version: ModelVersion = Field(..., description="Model version details")
    dataset: Dataset = Field(..., description="Dataset details")
    experiment_uuid: str = Field(..., description="UUID of the experiment")

    def is_training_initiated(self) -> bool:
        """Check if the training has been initiated."""
        return self.status == "training_initiated"

    def get_training_summary(self) -> dict:
        """Return a summary of the training information."""
        return {
            "status": self.status,
            "model_version_uuid": getattr(self.model_version, "uuid", None),
            "dataset_uuid": getattr(self.dataset, "uuid", None),
            "experiment_uuid": self.experiment_uuid,
        }

    class Config:
        """Example for the documentation."""

        json_schema_extra = {
            "example": {
                "status": "training_initiated",
                "model_version": {
                    "uuid": "uuid_R50v1",
                    "name": "ResNet50 v1",
                    "description": "ResNet50 model trained on Euro coins dataset",
                },
                "dataset": {
                    "uuid": "uuid_S1",
                    "name": "Euro Coins 2023",
                    "description": "Dataset containing European coins from 2023",
                },
                "experiment_uuid": "uuid_E1",
            }
        }

        @classmethod
        def model_json_schema_extra(cls, schema: dict) -> None:
            """Add extra information to the schema."""
            schema.update(
                {
                    "title": "Train Response",
                    "description": "Response model for the train endpoint",
                }
            )

        @classmethod
        def get_example(cls) -> dict:
            """Return the example data."""
            return cls.json_schema_extra["example"]


@router.post(
    "",
    response_model=TrainResponse,
    summary="Initiate Model Training",
    description="Initiates training of already setup scenario for "
    "a specific model version and dataset.",
)
@api_route_handler("initiating model training")
async def train_model(request: Request, train_request: TrainRequest) -> TrainResponse:
    """Initiate model training."""
    # Get the profile from the request header
    profile = get_supabase_profile(request)

    # Use TrainingDataService to fetch and prepare all required data
    training_data = await TrainingDataService.get_training_data(
        model_run_uuid=train_request.model_run_uuid,
        profile=profile,
    )

    # Use the service to prepare training data objects
    try:
        # This centralizes the object creation and validation logic in the service layer
        model_version, dataset, experiment_uuid = (
            await TrainingDataService.prepare_training_data(training_data)
        )

        # Note: training_parameters and model_data are still available in the
        # training_data dict when needed for the actual training implementation:
        # model_data = training_data["model"]  # For model initialization
        # training_parameters = training_data["training_parameters"]  # For parameters
    except TrainingDataError as e:
        # Handle validation errors more gracefully
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=f"Validation error while initiating model training: {str(e)}",
        ) from e

    # Use the training data to initialize and train the model
    # This is where you would integrate with your model training pipeline
    # The training_parameters contains the parameters from the model version
    # The model_data contains the model architecture information
    # The dataset_data contains the dataset information

    # Example of how you might start the training process:
    # 1. Initialize model with model_data and model_version_data
    # 2. Prepare dataset using dataset_data
    # 3. Start training with training_parameters
    # 4. Update model run status as training progresses

    # For now, we'll just log that we've prepared the data
    print(f"Prepared training data for model version: {model_version.uuid}")
    print(f"Using dataset: {dataset.uuid} with {dataset.images_count} images")

    # experiment_uuid is now provided by prepare_training_data

    return TrainResponse(
        status="training_initiated",
        model_version=model_version,
        dataset=dataset,
        experiment_uuid=experiment_uuid,
    )
