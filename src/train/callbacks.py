# src/train/callbacks.py
"""
Defines the callback system for the ModelTrainer.

Callbacks can be used to customize the behavior of the training loop at various stages.
"""

from __future__ import annotations

import asyncio
import logging
from datetime import datetime
from typing import TYPE_CHECKING, Any, Dict, List, Optional, Union
from uuid import UUID

import torch

if TYPE_CHECKING:
    from src.train.trainer import ModelTrainer

logger = logging.getLogger(__name__)


class Callback:
    """
    Abstract base class for creating callbacks.

    Callbacks can be used to customize the behavior of the ModelTrainer during training.
    Subclasses can override the methods for the events they are interested in.
    """

    def __init__(self) -> None:
        self.trainer: ModelTrainer | None = None

    def set_trainer(self, trainer: ModelTrainer) -> None:
        """Sets the trainer instance for the callback."""
        self.trainer = trainer

    def on_train_begin(self, logs: Dict[str, Any] | None = None) -> None:
        """Called at the beginning of training."""

    def on_train_end(self, logs: Dict[str, Any] | None = None) -> None:
        """Called at the end of training."""

    def on_epoch_begin(self, epoch: int, logs: Dict[str, Any] | None = None) -> None:
        """Called at the beginning of an epoch."""

    def on_epoch_end(self, epoch: int, logs: Dict[str, Any] | None = None) -> None:
        """Called at the end of an epoch."""

    def on_batch_begin(self, batch: int, logs: Dict[str, Any] | None = None) -> None:
        """Called at the beginning of a training batch."""

    def on_batch_end(self, batch: int, logs: Dict[str, Any] | None = None) -> None:
        """Called at the end of a training batch."""

    def on_validation_begin(self, logs: Dict[str, Any] | None = None) -> None:
        """Called at the beginning of a validation run."""

    def on_validation_end(self, logs: Dict[str, Any] | None = None) -> None:
        """Called at the end of a validation run."""


class EarlyStoppingCallback(Callback):  # pylint: disable=too-many-instance-attributes
    """
    Callback to stop training when a monitored metric has stopped improving.
    """

    def __init__(  # pylint: disable=too-many-arguments
        self,
        monitor: str = "validation_loss",
        patience: int = 5,
        min_delta: float = 0.0,
        mode: str = "min",
        *,  # Mark subsequent arguments as keyword-only
        verbose: bool = True,
    ):
        super().__init__()
        self.monitor = monitor
        self.patience = patience
        self.min_delta = min_delta
        self.mode = mode
        self.verbose = verbose
        self.wait = 0
        self.best_score = float("inf") if self.mode == "min" else float("-inf")

        if self.mode not in ["min", "max"]:
            raise ValueError(
                f"EarlyStopping mode '{self.mode}' is unknown, please use 'min' or 'max'."
            )

    def on_epoch_end(self, epoch: int, logs: Dict[str, Any] | None = None) -> None:
        logs = logs or {}
        current_score = logs.get(self.monitor)

        if current_score is None:
            logger.warning(
                "Early stopping conditioned on unavailable metric `%s`. Available metrics are: %s",
                self.monitor,
                ",".join(logs.keys()),
            )
            return

        # Check if this is the first time a score is being set
        is_first_score = self.best_score in [float("inf"), float("-inf")]

        if self.mode == "min":
            improved = current_score < self.best_score - self.min_delta
        else:  # mode == "max"
            improved = current_score > self.best_score + self.min_delta

        if improved:
            previous_best = self.best_score
            self.best_score = current_score
            self.wait = 0
            # Only log improvement if it's not the very first score being set
            if self.verbose and not is_first_score:
                logger.info(
                    "Epoch %s: %s improved from %.4f to %.4f. Resetting patience.",
                    epoch,
                    self.monitor,
                    previous_best,
                    self.best_score,
                )
        else:
            self.wait += 1
            if self.verbose:
                logger.info(
                    "Epoch %s: %s did not improve from %.4f. Patience: %s/%s",
                    epoch,
                    self.monitor,
                    self.best_score,
                    self.wait,
                    self.patience,
                )

        if self.wait >= self.patience:
            if self.verbose:
                logger.info(
                    "Epoch %s: Stopping training early as %s did not improve for %s epochs.",
                    epoch,
                    self.monitor,
                    self.patience,
                )
            if self.trainer:
                self.trainer.stop_training = True


class ModelCheckpoint(Callback):
    """
    Callback to save the model checkpoint.

    This callback saves the model's weights at the end of every epoch
    if the monitored metric has improved.
    """

    def __init__(  # pylint: disable=too-many-arguments
        self,
        filepath: str,
        monitor: str = "validation_loss",
        mode: str = "min",
        *,
        save_best_only: bool = True,
        verbose: bool = True,
    ):
        super().__init__()
        self.filepath = filepath
        self.monitor = monitor
        self.mode = mode
        self.save_best_only = save_best_only
        self.verbose = verbose
        self.best_score = float("inf") if self.mode == "min" else float("-inf")

        if self.mode not in ["min", "max"]:
            raise ValueError(
                f"ModelCheckpoint mode '{self.mode}' is unknown, use 'min' or 'max'."
            )

    def on_epoch_end(self, epoch: int, logs: Dict[str, Any] | None = None) -> None:
        logs = logs or {}
        current_score = logs.get(self.monitor)

        if current_score is None:
            logger.warning(
                "ModelCheckpoint conditioned on unavailable metric `%s`. Available metrics are: %s",
                self.monitor,
                ",".join(logs.keys()),
            )
            return

        if self.mode == "min":
            improved = current_score < self.best_score
        else:  # mode == "max"
            improved = current_score > self.best_score

        if improved:
            previous_best = self.best_score
            self.best_score = current_score

            if self.trainer and self.trainer.components.model:
                if self.verbose:
                    logger.info(
                        "Epoch %d: %s improved from %.4f to %.4f, saving model to %s",
                        epoch,
                        self.monitor,
                        previous_best,
                        self.best_score,
                        self.filepath,
                    )
                torch.save(self.trainer.components.model.state_dict(), self.filepath)


class CallbackHandler:
    """
    Handles a list of callbacks and orchestrates their execution.
    """

    def __init__(self, callbacks: List[Callback] | None, trainer: ModelTrainer):
        self.callbacks = callbacks if callbacks else []
        self._set_trainer_on_callbacks(trainer)

    def _set_trainer_on_callbacks(self, trainer: ModelTrainer) -> None:
        for callback in self.callbacks:
            callback.set_trainer(trainer)

    def on_train_begin(self, logs: Dict[str, Any] | None = None) -> None:
        """Trigger the on_train_begin hook on all callbacks."""
        for callback in self.callbacks:
            callback.on_train_begin(logs)

    def on_train_end(self, logs: Dict[str, Any] | None = None) -> None:
        """Trigger the on_train_end hook on all callbacks."""
        for callback in self.callbacks:
            callback.on_train_end(logs)

    def on_epoch_begin(self, epoch: int, logs: Dict[str, Any] | None = None) -> None:
        """Trigger the on_epoch_begin hook on all callbacks."""
        for callback in self.callbacks:
            callback.on_epoch_begin(epoch, logs)

    def on_epoch_end(self, epoch: int, logs: Dict[str, Any] | None = None) -> None:
        """Trigger the on_epoch_end hook on all callbacks."""
        for callback in self.callbacks:
            callback.on_epoch_end(epoch, logs)

    def on_batch_begin(self, batch: int, logs: Dict[str, Any] | None = None) -> None:
        """Trigger the on_batch_begin hook on all callbacks."""
        for callback in self.callbacks:
            callback.on_batch_begin(batch, logs)

    def on_batch_end(self, batch: int, logs: Dict[str, Any] | None = None) -> None:
        """Trigger the on_batch_end hook on all callbacks."""
        for callback in self.callbacks:
            callback.on_batch_end(batch, logs)

    def on_validation_begin(self, logs: Dict[str, Any] | None = None) -> None:
        """Trigger the on_validation_begin hook on all callbacks."""
        for callback in self.callbacks:
            callback.on_validation_begin(logs)

    def on_validation_end(self, logs: Dict[str, Any] | None = None) -> None:
        """Trigger the on_validation_end hook on all callbacks."""
        for callback in self.callbacks:
            callback.on_validation_end(logs)


class ModelRunCallback(Callback):
    """
    Callback to update model run data in the database during training.

    This callback integrates with the ModelRunService to update timing fields,
    metrics, and log paths throughout the training process.
    """

    def __init__(
        self,
        model_run_uuid: Union[str, UUID],
        profile: Optional[str] = None,
        *,
        update_frequency: int = 1,
        verbose: bool = True,
    ):
        """
        Initialize the ModelRunCallback.

        Args:
            model_run_uuid: UUID of the model run to update
            profile: Database profile to use (e.g., 'development', 'production')
            update_frequency: How often to update metrics (every N epochs)
            verbose: Whether to log update operations
        """
        super().__init__()
        self.model_run_uuid = str(model_run_uuid)
        self.profile = profile
        self.update_frequency = update_frequency
        self.verbose = verbose
        self.start_time: Optional[datetime] = None

        # Import here to avoid circular imports
        try:
            from src.database.services.model_run_service import (
                ModelRunCompleteUpdate,
                ModelRunMetricsUpdate,
                ModelRunService,
                ModelRunServiceError,
                ModelRunTimingUpdate,
            )

            self.ModelRunService = ModelRunService
            self.ModelRunServiceError = ModelRunServiceError
            self.ModelRunTimingUpdate = ModelRunTimingUpdate
            self.ModelRunMetricsUpdate = ModelRunMetricsUpdate
            self.ModelRunCompleteUpdate = ModelRunCompleteUpdate
        except ImportError as e:
            logger.error("Failed to import ModelRunService: %s", str(e))
            raise

    def on_train_begin(self, logs: Dict[str, Any] | None = None) -> None:
        """Called at the beginning of training."""
        self.start_time = datetime.now()

        if self.verbose:
            logger.info(
                "ModelRunCallback: Starting training for model run %s",
                self.model_run_uuid,
            )

        # Update start time in database
        self._run_async_update(self._update_start_time)

    def on_train_end(self, logs: Dict[str, Any] | None = None) -> None:
        """Called at the end of training."""
        end_time = datetime.now()

        if self.verbose:
            logger.info(
                "ModelRunCallback: Training completed for model run %s",
                self.model_run_uuid,
            )

        # Prepare final metrics from trainer
        final_metrics = self._prepare_final_metrics(logs)

        # Update end time, final metrics, and log path in database
        self._run_async_update(self._update_training_complete, end_time, final_metrics)

    def on_epoch_end(self, epoch: int, logs: Dict[str, Any] | None = None) -> None:
        """Called at the end of an epoch."""
        # Only update metrics every N epochs to avoid excessive database calls
        if (epoch + 1) % self.update_frequency == 0:
            if self.verbose:
                logger.debug(
                    "ModelRunCallback: Updating metrics for epoch %d, model run %s",
                    epoch + 1,
                    self.model_run_uuid,
                )

            # Prepare epoch metrics
            epoch_metrics = self._prepare_epoch_metrics(epoch, logs)

            # Update metrics in database
            self._run_async_update(self._update_epoch_metrics, epoch_metrics)

    def _run_async_update(self, coro_func, *args) -> None:
        """
        Run an async update function in the current event loop or create a new one.

        Args:
            coro_func: The async function to run
            *args: Arguments to pass to the function
        """
        try:
            # Try to get the current event loop
            loop = asyncio.get_event_loop()
            if loop.is_running():
                # If loop is running, schedule the coroutine as a task
                asyncio.create_task(coro_func(*args))
            else:
                # If no loop is running, run the coroutine
                loop.run_until_complete(coro_func(*args))
        except RuntimeError:
            # No event loop exists, create a new one
            asyncio.run(coro_func(*args))
        except Exception as e:
            logger.error(
                "ModelRunCallback: Error running async update for model run %s: %s",
                self.model_run_uuid,
                str(e),
            )

    async def _update_start_time(self) -> None:
        """Update the start time in the database."""
        try:
            update_data = self.ModelRunTimingUpdate(
                model_run_uuid=self.model_run_uuid,
                start_time=self.start_time,
                profile=self.profile,
            )
            await self.ModelRunService.update_model_run_times(update_data)

            if self.verbose:
                logger.info(
                    "ModelRunCallback: Updated start time for model run %s",
                    self.model_run_uuid,
                )
        except self.ModelRunServiceError as e:
            logger.error(
                "ModelRunCallback: Failed to update start time for model run %s: %s",
                self.model_run_uuid,
                str(e),
            )
        except Exception as e:
            logger.error(
                "ModelRunCallback: Unexpected error updating start time for model run %s: %s",
                self.model_run_uuid,
                str(e),
            )

    async def _update_epoch_metrics(self, metrics: Dict[str, Any]) -> None:
        """Update epoch metrics in the database."""
        try:
            update_data = self.ModelRunMetricsUpdate(
                model_run_uuid=self.model_run_uuid,
                metrics=metrics,
                profile=self.profile,
            )
            await self.ModelRunService.update_model_run_metrics(update_data)

            if self.verbose:
                logger.debug(
                    "ModelRunCallback: Updated epoch metrics for model run %s",
                    self.model_run_uuid,
                )
        except self.ModelRunServiceError as e:
            logger.error(
                "ModelRunCallback: Failed to update epoch metrics for model run %s: %s",
                self.model_run_uuid,
                str(e),
            )
        except Exception as e:
            logger.error(
                "ModelRunCallback: Unexpected error updating epoch metrics for model run %s: %s",
                self.model_run_uuid,
                str(e),
            )

    async def _update_training_complete(
        self, end_time: datetime, final_metrics: Dict[str, Any]
    ) -> None:
        """Update training completion data in the database."""
        try:
            update_data = self.ModelRunCompleteUpdate(
                model_run_uuid=self.model_run_uuid,
                start_time=self.start_time,
                end_time=end_time,
                metrics=final_metrics,
                log_path=True,  # Indicate that logs are available
                profile=self.profile,
            )
            await self.ModelRunService.update_model_run_complete(update_data)

            if self.verbose:
                logger.info(
                    "ModelRunCallback: Updated training completion for model run %s",
                    self.model_run_uuid,
                )
        except self.ModelRunServiceError as e:
            logger.error(
                "ModelRunCallback: Failed to update training completion for model run %s: %s",
                self.model_run_uuid,
                str(e),
            )
        except Exception as e:
            logger.error(
                "ModelRunCallback: Unexpected error updating training completion for model run %s: %s",
                self.model_run_uuid,
                str(e),
            )

    def _prepare_epoch_metrics(
        self, epoch: int, logs: Dict[str, Any] | None
    ) -> Dict[str, Any]:
        """
        Prepare epoch metrics for database storage.

        Args:
            epoch: Current epoch number
            logs: Training logs from the epoch

        Returns:
            Dictionary containing formatted metrics
        """
        logs = logs or {}

        # Extract key metrics from logs
        metrics = {
            "epoch": epoch + 1,  # Convert to 1-based indexing
            "timestamp": datetime.now().isoformat(),
        }

        # Add training metrics if available
        if "train_loss" in logs:
            metrics["train_loss"] = float(logs["train_loss"])
        if "train_accuracy" in logs:
            metrics["train_accuracy"] = float(logs["train_accuracy"])

        # Add validation metrics if available
        if "validation_loss" in logs:
            metrics["validation_loss"] = float(logs["validation_loss"])
        if "validation_accuracy" in logs:
            metrics["validation_accuracy"] = float(logs["validation_accuracy"])

        # Add any additional metrics from logs
        for key, value in logs.items():
            if key not in metrics and isinstance(value, (int, float)):
                metrics[key] = float(value)

        return metrics

    def _prepare_final_metrics(self, logs: Dict[str, Any] | None) -> Dict[str, Any]:
        """
        Prepare final training metrics for database storage.

        Args:
            logs: Final training logs

        Returns:
            Dictionary containing formatted final metrics
        """
        logs = logs or {}

        # Get metrics from trainer if available
        final_metrics = {
            "training_completed": True,
            "completion_timestamp": datetime.now().isoformat(),
        }

        # Add final epoch metrics
        if logs:
            final_metrics.update(
                self._prepare_epoch_metrics(logs.get("epoch", 0), logs)
            )

        # Add trainer metrics if trainer is available
        if self.trainer and hasattr(self.trainer, "metrics"):
            trainer_metrics = self.trainer.get_metrics()

            # Add timing information
            if "timing" in trainer_metrics:
                timing = trainer_metrics["timing"]
                if "total_training_time" in timing:
                    final_metrics["total_training_time"] = timing["total_training_time"]
                if "epoch_times" in timing and timing["epoch_times"]:
                    final_metrics["average_epoch_time"] = sum(
                        timing["epoch_times"]
                    ) / len(timing["epoch_times"])

            # Add final accuracies and losses
            if "train_losses" in trainer_metrics and trainer_metrics["train_losses"]:
                final_metrics["final_train_loss"] = trainer_metrics["train_losses"][-1]
            if (
                "train_accuracies" in trainer_metrics
                and trainer_metrics["train_accuracies"]
            ):
                final_metrics["final_train_accuracy"] = trainer_metrics[
                    "train_accuracies"
                ][-1]
            if "test_losses" in trainer_metrics and trainer_metrics["test_losses"]:
                final_metrics["final_validation_loss"] = trainer_metrics["test_losses"][
                    -1
                ]
            if (
                "test_accuracies" in trainer_metrics
                and trainer_metrics["test_accuracies"]
            ):
                final_metrics["final_validation_accuracy"] = trainer_metrics[
                    "test_accuracies"
                ][-1]

            # Add error information if present
            if "error" in trainer_metrics and trainer_metrics["error"]:
                final_metrics["error"] = trainer_metrics["error"]

        return final_metrics
