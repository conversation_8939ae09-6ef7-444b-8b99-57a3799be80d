"""
This module defines the ModelTrainer class, which encapsulates the logic for
training and evaluating a PyTorch model.
"""

import dataclasses
import json
import time
import traceback
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
from uuid import UUID

import numpy as np
import psutil
import torch
from tqdm import tqdm

from src.train.callbacks import Callback, CallbackHandler
from src.train.data_classes import TrainingComponents, TrainingConfig, TrainingMetrics
from src.utils.device import select_device
from src.utils.logging import setup_training_logger


class ModelTrainer:  # pylint: disable=too-many-instance-attributes
    """
    A class to handle the training and evaluation of a PyTorch model.
    """

    def __init__(
        self,
        model_components: Dict[str, Any],
        data_loaders: Dict[str, Any],
        training_config: Dict[str, Any],
        callbacks: List[Callback] | None = None,
        *,
        model_run_uuid: Optional[Union[str, UUID]] = None,
        db_profile: Optional[str] = None,
    ):
        """
        Initializes the ModelTrainer.

        Args:
            model_components (dict): A dictionary containing the core training components:
                - model (torch.nn.Module): The neural network model to train.
                - loss_fn: The loss function.
                - optimizer: The optimization algorithm.
            data_loaders (dict): A dictionary containing 'train' and 'test' DataLoaders.
            training_config (dict): Configuration for the training run.
            callbacks (list): A list of Callback instances
            to be executed at various stages of training.
            model_run_uuid (str | UUID, optional): UUID of the model run for database updates.
                If provided, a ModelRunCallback will be automatically added.
            db_profile (str, optional): Database profile to use for updates (e.g., 'development').
                Only used when model_run_uuid is provided.
        """
        self.components = TrainingComponents(
            model=model_components["model"],
            loss_fn=model_components["loss_fn"],
            optimizer=model_components["optimizer"],
        )
        self.data_loaders = data_loaders
        self.config = TrainingConfig(training_config)
        self.metrics = TrainingMetrics()

        self.device = select_device()
        self.components.model.to(self.device)

        self.logger = setup_training_logger(
            self.config.run_output_dir, self.config.model_id
        )

        # Prepare callbacks list, adding ModelRunCallback if database parameters are provided
        callbacks_list = list(callbacks) if callbacks else []

        # Add ModelRunCallback if model_run_uuid is provided
        if model_run_uuid is not None:
            try:
                from src.train.callbacks import ModelRunCallback

                db_callback = ModelRunCallback(
                    model_run_uuid=model_run_uuid,
                    profile=db_profile,
                    verbose=True,
                )
                callbacks_list.append(db_callback)

                self.logger.info(
                    "Added ModelRunCallback for model run %s with profile %s",
                    model_run_uuid,
                    db_profile or "default",
                )
            except ImportError as e:
                self.logger.warning(
                    "Failed to import ModelRunCallback, database updates will be disabled: %s",
                    str(e),
                )

        self.callback_handler = CallbackHandler(callbacks_list, self)

        self.logger.info("Initializing ModelTrainer for model %s", self.config.model_id)
        self.logger.info("Using device: %s", self.device)
        self.is_training = False
        self.stop_training = False  # Flag to allow callbacks to stop training

        # Learning rate schedule
        self.lr_epoch_map: Dict[int, float] = {}
        if self.config.learning_rate_schedule:
            for item in self.config.learning_rate_schedule:
                if isinstance(item, dict) and "epoch" in item and "rate" in item:
                    self.lr_epoch_map[item["epoch"]] = item["rate"]
                elif hasattr(item, "epoch") and hasattr(
                    item, "rate"
                ):  # Handle Pydantic model objects
                    self.lr_epoch_map[item.epoch] = item.rate
                else:
                    self.logger.warning(
                        "Invalid item in learning_rate_schedule: %s. Skipping.", item
                    )
            if self.lr_epoch_map:
                self.logger.info("Learning rate schedule loaded: %s", self.lr_epoch_map)

        # Gradient clipping logging
        if (
            self.config.gradient_clip_max_norm
            and self.config.gradient_clip_max_norm > 0
        ):
            self.logger.info(
                "Gradient clipping enabled with max_norm: %s",
                self.config.gradient_clip_max_norm,
            )

    def train(
        self,
    ):  # pylint: disable=too-many-locals, too-many-branches, too-many-statements
        """
        Train the model for the specified number of epochs.
        """
        try:
            start_epoch = 0
            self.is_training = True

            self.callback_handler.on_train_begin()
            self.metrics.timing["start_time"] = time.time()
            epochs = self.config.epochs
            self.logger.info("Starting model training for %s epochs...", epochs)

            for epoch in range(start_epoch, epochs):
                self.callback_handler.on_epoch_begin(epoch)
                # Apply learning rate schedule
                if epoch in self.lr_epoch_map:
                    new_lr = self.lr_epoch_map[epoch]
                    for param_group in self.components.optimizer.param_groups:
                        param_group["lr"] = new_lr
                    self.logger.info(
                        "Epoch %s: Learning rate set to %s by schedule.",
                        epoch + 1,
                        new_lr,
                    )

                epoch_start = time.time()
                self._collect_resource_metrics()

                train_metrics = self._train_epoch(epoch)
                validation_metrics = self._validate_epoch(epoch)

                # Append metrics for this epoch
                self.metrics.train_losses.append(train_metrics["train_loss"])
                self.metrics.train_accuracies.append(train_metrics["train_accuracy"])
                self.metrics.test_losses.append(validation_metrics["validation_loss"])
                self.metrics.test_accuracies.append(
                    validation_metrics["validation_accuracy"]
                )

                epoch_end = time.time()
                self.metrics.timing["epoch_times"].append(epoch_end - epoch_start)

                self.logger.info(
                    "Epoch %s/%s - Train: loss=%.4f, acc=%.2f%% | Val: loss=%.4f, acc=%.2f%%",
                    epoch + 1,
                    epochs,
                    train_metrics["train_loss"],
                    train_metrics["train_accuracy"] * 100,
                    validation_metrics["validation_loss"],
                    validation_metrics["validation_accuracy"] * 100,
                )
                # Create a log dictionary for callbacks
                epoch_logs = {
                    "epoch": epoch,
                    **train_metrics,
                    **validation_metrics,
                }

                self.callback_handler.on_epoch_end(epoch, epoch_logs)

                if self.stop_training:
                    self.logger.info(
                        "Stopping training early at epoch %s as requested by a callback.",
                        epoch,
                    )
                    break

            self.metrics.timing["end_time"] = time.time()
            total_time = (
                self.metrics.timing["end_time"] - self.metrics.timing["start_time"]
            )
            self.metrics.timing["total_training_time"] = total_time

            self._save_artifacts()

            self.logger.info(
                "Training finished in %.2fs. Artifacts saved to %s",
                total_time,
                self.config.run_output_dir,
            )

            self.callback_handler.on_train_end()

        except KeyboardInterrupt:
            self.logger.info("Training interrupted by user (KeyboardInterrupt).")
            self.metrics.error = {
                "type": "KeyboardInterrupt",
                "message": "Training interrupted by user.",
            }
            # Optionally save checkpoint and artifacts on interrupt
            if getattr(self.config, "save_on_interrupt", True):
                self.logger.info("Saving artifacts due to interrupt...")
                self._save_artifacts()

        except Exception as e:  # pylint: disable=broad-except
            self.logger.error("Unhandled exception during training: %s", str(e))
            self.logger.error(traceback.format_exc())
            self.metrics.error = {"type": str(type(e).__name__), "message": str(e)}
            # Optionally save checkpoint and artifacts on error
            if getattr(self.config, "save_on_error", True):
                self.logger.info("Saving artifacts due to error...")
                self._save_artifacts()
            raise
        finally:
            self.is_training = False
        return self.metrics  # Return the TrainingMetrics object

    def _restore_metrics(self, checkpoint_data):
        """Helper to restore metrics from a checkpoint."""
        if "metrics" in checkpoint_data:
            loaded_metrics = checkpoint_data["metrics"]
            if isinstance(
                loaded_metrics, dict
            ):  # For nested dicts like 'timing' or 'resources'
                for dict_name in ["timing", "resources", "custom"]:
                    nested_dict = getattr(self.metrics, dict_name, None)
                    if isinstance(nested_dict, dict):
                        for key, value in loaded_metrics.get(dict_name, {}).items():
                            nested_dict[key] = value
            elif isinstance(loaded_metrics, TrainingMetrics):  # New format
                self.metrics = loaded_metrics
                self.logger.info("Restored metrics from TrainingMetrics object.")
            else:
                self.logger.warning(
                    "Metrics in checkpoint are in an unrecognized format. Skipping restore."
                )

    def _save_metrics_to_file(self, output_dir: Path):
        """Helper to save metrics to JSON files."""
        self.logger.info("Saving metrics files to %s", output_dir)
        # Save detailed history
        detailed_metrics_path = output_dir / "metrics_history.json"
        with open(detailed_metrics_path, "w", encoding="utf-8") as f:
            json.dump(dataclasses.asdict(self.metrics), f, indent=4)
        self.logger.info("Saved detailed metrics history to %s", detailed_metrics_path)

        # Save summary
        summary_metrics_path = output_dir / "metrics_summary.json"
        with open(summary_metrics_path, "w", encoding="utf-8") as f:
            summary_data = (
                self.metrics.get_metric_summary()
            )  # Assuming this returns a dict
            json.dump(summary_data, f, indent=4)
        self.logger.info("Saved metrics summary to %s", summary_metrics_path)

    def _save_artifacts(self):
        """
        Save model artifacts, including the trained model and metrics.
        """
        self.logger.info("Saving artifacts...")
        try:
            output_dir = Path(self.config.run_output_dir)
            output_dir.mkdir(parents=True, exist_ok=True)

            # Save the final model state.
            final_model_path = output_dir / "model.pt"
            if self.components.model:
                torch.save(self.components.model.state_dict(), str(final_model_path))
                self.logger.info("Saved final model state to %s", final_model_path)
            else:
                self.logger.warning("Model component is None, skipping model save.")

            # Save metrics files.
            self._save_metrics_to_file(output_dir)

        except Exception as e:
            self.logger.error("Error saving artifacts: %s", str(e))
            self.logger.error(traceback.format_exc())

    def _train_epoch(self, epoch):
        """
        Runs a single training epoch.
        """
        train_start = time.time()
        self.components.model.train()
        batch_losses = []
        batch_accuracies = []
        batch_metrics_list = []

        train_loader_tqdm = tqdm(
            self.data_loaders["train"], desc=f"Epoch {epoch+1} Training", leave=False
        )

        for batch_idx, (X, y) in enumerate(train_loader_tqdm):
            batch_start_time = time.time()
            X, y = X.to(self.device), y.to(self.device)

            self.components.optimizer.zero_grad()
            yHat = self.components.model(X)
            loss = self.components.loss_fn(yHat, y)
            loss.backward()

            # Gradient Clipping
            if (
                self.config.gradient_clip_max_norm
                and self.config.gradient_clip_max_norm > 0
            ):
                torch.nn.utils.clip_grad_norm_(
                    self.components.model.parameters(),
                    max_norm=self.config.gradient_clip_max_norm,
                )

            self.components.optimizer.step()

            batch_loss = loss.item()
            batch_accuracy = torch.mean(((yHat > 0) == y).float()).item() * 100
            batch_losses.append(batch_loss)
            batch_accuracies.append(batch_accuracy)

            train_loader_tqdm.set_postfix(
                {"loss": f"{batch_loss:.4f}", "acc": f"{batch_accuracy:.2f}%"}
            )

            batch_metrics_list.append(
                {
                    "epoch": epoch,
                    "batch": batch_idx,
                    "loss": batch_loss,
                    "accuracy": batch_accuracy,
                    "time": time.time() - batch_start_time,
                }
            )

        self.metrics.batch_metrics.extend(batch_metrics_list)
        self.metrics.timing["train_times"].append(time.time() - train_start)

        return {
            "train_loss": np.mean(batch_losses) if batch_losses else 0,
            "train_accuracy": np.mean(batch_accuracies) if batch_accuracies else 0,
        }

    def _validate_epoch(self, epoch):
        """
        Runs a validation epoch.
        """
        validation_start = time.time()
        self.components.model.eval()
        all_losses = []
        all_accuracies = []

        val_loader_tqdm = tqdm(
            self.data_loaders["test"], desc=f"Epoch {epoch+1} Validation", leave=False
        )

        with torch.no_grad():
            for X, y in val_loader_tqdm:
                X, y = X.to(self.device), y.to(self.device)
                yHat = self.components.model(X)
                loss = self.components.loss_fn(yHat, y)

                batch_loss = loss.item()
                batch_accuracy = torch.mean(((yHat > 0) == y).float()).item() * 100
                all_losses.append(batch_loss)
                all_accuracies.append(batch_accuracy)

                val_loader_tqdm.set_postfix(
                    {
                        "val_loss": f"{batch_loss:.4f}",
                        "val_acc": f"{batch_accuracy:.2f}%",
                    }
                )

        avg_loss = np.mean(all_losses) if all_losses else 0
        avg_accuracy = np.mean(all_accuracies) if all_accuracies else 0

        self.metrics.timing["validation_times"].append(time.time() - validation_start)

        return {"validation_loss": avg_loss, "validation_accuracy": avg_accuracy}

    def _collect_resource_metrics(self):
        """
        Collects system resource usage metrics.
        """
        try:
            cpu_percent = psutil.cpu_percent(interval=0.1)
            self.metrics.resources["cpu_percent"].append(cpu_percent)

            memory = psutil.virtual_memory()
            self.metrics.resources["memory_percent"].append(memory.percent)
            self.metrics.resources["memory_used_gb"].append(memory.used / (1024**3))
            self.metrics.resources["memory_available_gb"].append(
                memory.available / (1024**3)
            )

            disk = psutil.disk_usage("/")
            self.metrics.resources["disk_percent"].append(disk.percent)

            if torch.cuda.is_available():
                gpu_memory_allocated = torch.cuda.memory_allocated() / (1024**3)
                gpu_memory_reserved = torch.cuda.memory_reserved() / (1024**3)
                self.metrics.resources["gpu_memory_used"].append(gpu_memory_allocated)
                self.metrics.resources["gpu_memory_reserved"].append(
                    gpu_memory_reserved
                )

                if len(self.metrics.resources["cpu_percent"]) % 10 == 0:
                    self.logger.debug(
                        "Resource usage - CPU: %.1f%%, Memory: %.1f%%, GPU Memory: %.2fGB/%.2fGB",
                        cpu_percent,
                        memory.percent,
                        gpu_memory_allocated,
                        gpu_memory_reserved,
                    )
            elif len(self.metrics.resources["cpu_percent"]) % 10 == 0:
                self.logger.debug(
                    "Resource usage - CPU: %.1f%%, Memory: %.1f%%",
                    cpu_percent,
                    memory.percent,
                )
        except Exception as e:
            self.logger.warning("Error collecting resource metrics: %s", str(e))

    def add_custom_metric(self, name, value):
        """
        Adds a custom metric to the metrics collection.
        """
        if name not in self.metrics.custom_metrics:
            self.metrics.custom_metrics[name] = []
        self.metrics.custom_metrics[name].append(value)

    def get_metrics(self) -> Dict[str, Any]:
        """
        Returns a dictionary of metrics.
        """
        return dataclasses.asdict(self.metrics)
