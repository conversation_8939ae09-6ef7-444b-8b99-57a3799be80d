#!/usr/bin/env python3
"""
Example usage of the ModelRunCallback for database integration during training.

This example demonstrates how to use the ModelTrainer with automatic database
updates through the ModelRunCallback system.
"""

import asyncio
import tempfile
from datetime import datetime
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock

# Mock the database service for demonstration
class MockModelRunService:
    """Mock service for example purposes."""
    
    @classmethod
    async def update_model_run_times(cls, update_data):
        print(f"🕐 Mock: Updated timing for model run {update_data.model_run_uuid}")
        print(f"   Start time: {update_data.start_time}")
        print(f"   Prepared time: {update_data.prepared_time}")
        print(f"   Profile: {update_data.profile}")
        return {"uuid": update_data.model_run_uuid, "status": "updated"}
    
    @classmethod
    async def update_model_run_metrics(cls, update_data):
        print(f"📊 Mock: Updated metrics for model run {update_data.model_run_uuid}")
        print(f"   Metrics: {update_data.metrics}")
        return {"uuid": update_data.model_run_uuid, "status": "updated"}
    
    @classmethod
    async def update_model_run_complete(cls, update_data):
        print(f"✅ Mock: Training completed for model run {update_data.model_run_uuid}")
        print(f"   End time: {update_data.end_time}")
        print(f"   Final metrics keys: {list(update_data.metrics.keys())}")
        print(f"   Log path available: {update_data.log_path}")
        return {"uuid": update_data.model_run_uuid, "status": "completed"}


def create_mock_trainer_components():
    """Create mock trainer components for demonstration."""
    import torch
    import torch.nn as nn
    import torch.optim as optim
    
    # Simple mock model
    model = nn.Sequential(
        nn.Linear(10, 5),
        nn.ReLU(),
        nn.Linear(5, 1),
        nn.Sigmoid()
    )
    
    # Mock components
    model_components = {
        "model": model,
        "loss_fn": nn.BCELoss(),
        "optimizer": optim.Adam(model.parameters(), lr=0.001),
    }
    
    # Mock data loaders
    data_loaders = {
        "train": MagicMock(),
        "test": MagicMock(),
    }
    
    return model_components, data_loaders


def example_trainer_with_database_integration():
    """Example: Using ModelTrainer with database integration."""
    print("=== Example: ModelTrainer with Database Integration ===")
    
    # Mock the ModelRunService imports in the callback
    import sys
    from unittest.mock import patch
    
    # Create a mock module for the database service
    mock_module = MagicMock()
    mock_module.ModelRunService = MockModelRunService
    mock_module.ModelRunServiceError = Exception
    mock_module.ModelRunTimingUpdate = MagicMock
    mock_module.ModelRunMetricsUpdate = MagicMock
    mock_module.ModelRunCompleteUpdate = MagicMock
    
    # Mock the import
    sys.modules['src.database.services.model_run_service'] = mock_module
    
    try:
        from src.train.trainer import ModelTrainer
        
        # Create mock components
        model_components, data_loaders = create_mock_trainer_components()
        
        # Training configuration
        with tempfile.TemporaryDirectory() as temp_dir:
            training_config = {
                "model_id": "example_model",
                "run_output_dir": temp_dir,
                "epochs": 3,  # Short training for demo
            }
            
            # Create trainer with database integration
            model_run_uuid = "123e4567-e89b-12d3-a456-************"
            db_profile = "development"
            
            print(f"🚀 Creating trainer with model run UUID: {model_run_uuid}")
            print(f"📊 Database profile: {db_profile}")
            
            trainer = ModelTrainer(
                model_components=model_components,
                data_loaders=data_loaders,
                training_config=training_config,
                model_run_uuid=model_run_uuid,  # Enable database updates
                db_profile=db_profile,          # Specify database profile
            )
            
            print("✅ Trainer created successfully with ModelRunCallback!")
            print("🔄 The callback will automatically update the database during training:")
            print("   - Start time when training begins")
            print("   - Metrics after each epoch")
            print("   - Completion data when training ends")
            
            # Note: In a real scenario, you would call trainer.train() here
            # For this demo, we'll just show that the callback was added
            callback_types = [type(cb).__name__ for cb in trainer.callback_handler.callbacks]
            print(f"📋 Active callbacks: {callback_types}")
            
            return trainer
            
    except ImportError as e:
        print(f"❌ Error importing trainer: {e}")
        return None


def example_manual_callback_usage():
    """Example: Using ModelRunCallback manually."""
    print("\n=== Example: Manual ModelRunCallback Usage ===")
    
    # Mock the database service imports
    import sys
    from unittest.mock import patch
    
    mock_module = MagicMock()
    mock_module.ModelRunService = MockModelRunService
    mock_module.ModelRunServiceError = Exception
    mock_module.ModelRunTimingUpdate = MagicMock
    mock_module.ModelRunMetricsUpdate = MagicMock
    mock_module.ModelRunCompleteUpdate = MagicMock
    
    sys.modules['src.database.services.model_run_service'] = mock_module
    
    try:
        from src.train.callbacks import ModelRunCallback
        
        # Create callback
        callback = ModelRunCallback(
            model_run_uuid="test-uuid-456",
            profile="development",
            update_frequency=2,  # Update every 2 epochs
            verbose=True,
        )
        
        print(f"📝 Created ModelRunCallback for UUID: {callback.model_run_uuid}")
        print(f"🔄 Update frequency: every {callback.update_frequency} epochs")
        
        # Simulate training events
        print("\n🏁 Simulating training start...")
        callback.on_train_begin()
        
        print("\n📈 Simulating epoch completions...")
        for epoch in range(5):
            logs = {
                "train_loss": 0.5 - epoch * 0.1,
                "train_accuracy": 0.6 + epoch * 0.08,
                "validation_loss": 0.6 - epoch * 0.08,
                "validation_accuracy": 0.55 + epoch * 0.09,
            }
            print(f"   Epoch {epoch + 1}: {logs}")
            callback.on_epoch_end(epoch, logs)
        
        print("\n🏆 Simulating training completion...")
        final_logs = {
            "epoch": 4,
            "train_loss": 0.1,
            "validation_loss": 0.2,
        }
        callback.on_train_end(final_logs)
        
        print("✅ Callback demonstration completed!")
        
    except ImportError as e:
        print(f"❌ Error importing callback: {e}")


def example_integration_with_existing_callbacks():
    """Example: Using ModelRunCallback with other callbacks."""
    print("\n=== Example: Integration with Existing Callbacks ===")
    
    import sys
    from unittest.mock import patch
    
    # Mock the database service
    mock_module = MagicMock()
    mock_module.ModelRunService = MockModelRunService
    mock_module.ModelRunServiceError = Exception
    mock_module.ModelRunTimingUpdate = MagicMock
    mock_module.ModelRunMetricsUpdate = MagicMock
    mock_module.ModelRunCompleteUpdate = MagicMock
    
    sys.modules['src.database.services.model_run_service'] = mock_module
    
    try:
        from src.train.trainer import ModelTrainer
        from src.train.callbacks import EarlyStoppingCallback, ModelCheckpoint
        
        # Create mock components
        model_components, data_loaders = create_mock_trainer_components()
        
        with tempfile.TemporaryDirectory() as temp_dir:
            training_config = {
                "model_id": "example_model_with_callbacks",
                "run_output_dir": temp_dir,
                "epochs": 10,
            }
            
            # Create additional callbacks
            early_stopping = EarlyStoppingCallback(
                monitor="validation_loss",
                patience=3,
                verbose=True
            )
            
            model_checkpoint = ModelCheckpoint(
                filepath=str(Path(temp_dir) / "best_model.pt"),
                monitor="validation_loss",
                save_best_only=True,
                verbose=True
            )
            
            # Create trainer with multiple callbacks including database integration
            trainer = ModelTrainer(
                model_components=model_components,
                data_loaders=data_loaders,
                training_config=training_config,
                callbacks=[early_stopping, model_checkpoint],  # Existing callbacks
                model_run_uuid="multi-callback-uuid",           # Database integration
                db_profile="development",
            )
            
            # Show all active callbacks
            callback_names = [type(cb).__name__ for cb in trainer.callback_handler.callbacks]
            print(f"🔧 Active callbacks: {callback_names}")
            print("✅ Successfully integrated ModelRunCallback with existing callbacks!")
            
            return trainer
            
    except ImportError as e:
        print(f"❌ Error: {e}")
        return None


async def main():
    """Run all examples."""
    print("ModelRunCallback Integration Examples")
    print("=" * 60)
    
    print("ℹ️  Note: These examples use mock implementations for demonstration.")
    print("ℹ️  In a real environment, the callbacks will update the actual database.")
    print()
    
    # Run examples
    example_trainer_with_database_integration()
    example_manual_callback_usage()
    example_integration_with_existing_callbacks()
    
    print("\n" + "=" * 60)
    print("🎉 All examples completed successfully!")
    print("\n💡 Key Benefits:")
    print("   • Automatic database updates during training")
    print("   • Seamless integration with existing callback system")
    print("   • Configurable update frequency to optimize performance")
    print("   • Robust error handling and logging")
    print("   • Easy to enable/disable via trainer parameters")


if __name__ == "__main__":
    asyncio.run(main())
